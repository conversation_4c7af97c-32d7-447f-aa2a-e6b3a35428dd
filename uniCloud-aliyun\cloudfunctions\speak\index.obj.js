// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const tencentcloud = require('tencentcloud-sdk-nodejs-asr')
const AsrClient = tencentcloud.asr.v20190614.Client
const { OpenAI } = require('openai')
const clientConfig = {
  credential: {
    secretId: 'AKIDgFc3Pm9PXn1756rW0JRLEvPgkdIkz2tX',
    secretKey: 'XuOo7FBxW4RbdVBdswscGl7HEyVqysbB',
  },
  region: '',
  profile: {
    httpProfile: {
      endpoint: 'asr.tencentcloudapi.com',
    },
  },
}

// AI 配置 - 参考 ai 云函数的配置
const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  apiKey: 'a1d08626-e9fa-425b-a8cf-e59f274d3a3d', // API 密钥
  timeout: 30000, // 请求超时时间（毫秒），30 秒超时
}

// HTML 转义函数
function escapeHtml(text) {
  if (typeof text !== 'string') return text
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

// 将 JSON 对象转换为 XML 标签格式
function jsonToXmlTags(jsonObj) {
  let xmlString = ''

  if (jsonObj.title) {
    xmlString += `<title>${escapeHtml(jsonObj.title)}</title>`
  }

  if (jsonObj.content) {
    xmlString += `<content>${escapeHtml(jsonObj.content)}</content>`
  }

  if (jsonObj.keyWord) {
    xmlString += `<keyword>${escapeHtml(jsonObj.keyWord)}</keyword>`
  }

  // 处理评价相关字段
  if (jsonObj['内容完整性']) {
    xmlString += `<content_completeness>${escapeHtml(jsonObj['内容完整性'])}</content_completeness>`
  }

  if (jsonObj['表达流畅度']) {
    xmlString += `<expression_fluency>${escapeHtml(jsonObj['表达流畅度'])}</expression_fluency>`
  }

  if (jsonObj['语法和词汇']) {
    xmlString += `<grammar_vocabulary>${escapeHtml(jsonObj['语法和词汇'])}</grammar_vocabulary>`
  }

  if (jsonObj['理解深度']) {
    xmlString += `<understanding_depth>${escapeHtml(jsonObj['理解深度'])}</understanding_depth>`
  }

  if (jsonObj['总体评价']) {
    xmlString += `<overall_evaluation>${escapeHtml(jsonObj['总体评价'])}</overall_evaluation>`
  }

  // 处理关键词故事评价字段
  if (jsonObj['关键词使用']) {
    xmlString += `<keyword_usage>${escapeHtml(jsonObj['关键词使用'])}</keyword_usage>`
  }

  if (jsonObj['故事创意']) {
    xmlString += `<story_creativity>${escapeHtml(jsonObj['故事创意'])}</story_creativity>`
  }

  if (jsonObj['AI 示例故事']) {
    xmlString += `<ai_example_story>${escapeHtml(jsonObj['AI 示例故事'])}</ai_example_story>`
  }

  if (jsonObj['故事改良版']) {
    xmlString += `<improved_story>${escapeHtml(jsonObj['故事改良版'])}</improved_story>`
  }

  return xmlString
}

// 通用 AI 响应解析函数（解析 JSON 后转换为 XML 标签格式）
function parseAIResponse(content) {
  console.log('原始 AI 响应内容:', content)

  // 移除可能包裹 JSON 的```json 和```标记
  if (content.includes('```json')) {
    content = content.replace(/```json|```/g, '').trim()
  }

  // 移除可能的 markdown 代码块标记
  if (content.includes('```')) {
    content = content.replace(/```[\s\S]*?```/g, '').trim()
  }

  // 尝试提取 JSON 内容
  let jsonContent = content.trim()

  // 如果内容不是以 { 开头，尝试找到第一个 {
  if (!jsonContent.startsWith('{')) {
    const jsonStart = jsonContent.indexOf('{')
    if (jsonStart !== -1) {
      jsonContent = jsonContent.substring(jsonStart)
    }
  }

  // 如果内容不是以 } 结尾，尝试找到最后一个 }
  if (!jsonContent.endsWith('}')) {
    const jsonEnd = jsonContent.lastIndexOf('}')
    if (jsonEnd !== -1) {
      jsonContent = jsonContent.substring(0, jsonEnd + 1)
    }
  }

  // 修复中文引号问题 - 将中文引号替换为英文引号
  jsonContent = jsonContent
    .replace(/"/g, '"') // 左双引号
    .replace(/"/g, '"') // 右双引号
    .replace(/'/g, "'") // 左单引号
    .replace(/'/g, "'") // 右单引号

  console.log('修复引号后的 JSON 内容:', jsonContent)

  try {
    const jsonObj = JSON.parse(jsonContent)
    const xmlString = jsonToXmlTags(jsonObj)
    console.log('转换后的 XML 标签格式:', xmlString)
    return xmlString
  } catch (error) {
    console.error('JSON 解析失败:', error.message)
    console.error('失败的内容:', jsonContent)
    throw new Error(`AI 响应格式错误: ${error.message}`)
  }
}
module.exports = {
  _before: function () {},
  async asr() {
    const httpInfo = this.getHttpInfo()
    console.log('httpInfo.body', httpInfo.body)
    let { url } = JSON.parse(httpInfo.body)

    const client = new AsrClient(clientConfig)
    const params = {
      EngineModelType: '16k_zh',
      ChannelNum: 1,
      ResTextFormat: 2,
      SourceType: 0,
      Url: url,
      // CallbackUrl: 'https://fc-mp-c6815a6a-de20-45ad-a345-2e804f127311.next.bspapp.com/speak/asrCallback',
    }

    try {
      const { Data } = await client.CreateRecTask(params)
      const asrId = Data.TaskId
      return {
        code: 200,
        data: {
          asrId,
        },
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: 'asr fail',
      }
    }
  },
  async getAsr() {
    const httpInfo = this.getHttpInfo()
    let { taskId } = JSON.parse(httpInfo.body)
    const client = new AsrClient(clientConfig)
    const params = {
      TaskId: taskId,
    }

    try {
      const data = await client.DescribeTaskStatus(params)

      return {
        code: 200,
        data,
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: 'asr fail',
      }
    }
  },

  /**
   * 获取所有普通话训练数据
   * 从云数据库 putonghua 表中查询所有训练数据
   * @returns {Object} 包含 code、message、data 的响应对象
   */
  async getPutonghuaData() {
    const db = uniCloud.database()
    const collection = db.collection('putonghua')

    try {
      console.log('开始查询普通话训练数据')
      const res = await collection.get()
      console.log('查询成功，数据条数：', res.data.length)

      return {
        code: 0,
        message: 'success',
        data: res.data,
      }
    } catch (error) {
      console.error('查询普通话数据失败：', error)
      return {
        code: -1,
        message: error.message || '查询失败',
        data: [],
      }
    }
  },

  /**
   * 生成复述素材
   * @param {string} story 故事内容
   * @returns {Object} 包含 title、content、keyWord 的响应对象
   */
  async generateRetellConetnt(story) {
    if (!story) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '故事内容不能为空',
      }
    }

    try {
      const openai = new OpenAI(doubaoParams)

      const params = {
        model: 'doubao-seed-1-6-flash-250715',
        messages: [
          {
            role: 'system',
            content: `# 角色
你是一位专业的内容创作者，擅长创作出有趣且富有讨论价值的内容，同时能精准提炼其中的关键信息。

## 技能
### 技能 1: 生成素材和关键词
1. 生成一段 200 - 300 字的素材内容，内容要求：
    - 主题要有趣且具有讨论价值；
    - 内容要逻辑清晰；
    - 难度适中，适合口头表达。
2. 从这段内容中提取 5 - 8 个关键词，这些关键词应该：
    - 能够概括内容的主要脉络；
    - 按照内容出现的顺序排列；
    - 包含核心人物、事件、地点或概念。
3. 为生成的素材内容拟定一个 title。
4. 以如下格式输出：
{
  "title": "生成的标题",
  "content": "素材内容",
  "keyWord": "关键词 1，关键词 2"
}

## 限制：
- 输出内容必须严格按照上述格式进行组织，不能偏离框架要求。`,
          },
          {
            role: 'user',
            content: '请根据以下故事生成复述素材：' + story,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }

      const completion = await openai.chat.completions.create(params)
      let content = completion.choices[0]?.message?.content

      if (!content) {
        throw new Error('AI 响应为空')
      }

      const result = parseAIResponse(content)

      // 直接返回解析后的结果，保持与原接口完全兼容
      return result
    } catch (error) {
      console.error('生成复述素材失败：', error)
      // 直接抛出异常，保持与原接口行为一致
      throw error
    }
  },

  /**
   * 点评复述
   * @param {string} retell 复述内容
   * @returns {Object} 包含评价结果的响应对象
   */
  async evaluateRetell(retell) {
    if (!retell) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '复述内容不能为空',
      }
    }

    try {
      const openai = new OpenAI(doubaoParams)

      const params = {
        model: 'doubao-seed-1-6-flash-250715',
        messages: [
          {
            role: 'system',
            content: `# 角色
你是一位专业的表达能力分析师，能够精准分析用户表达的优劣并给出针对性建议。

## 技能
### 技能 1: 表达分析
1. 从以下几个维度分析用户的表达：
    - 内容完整性（原始内容的重要信息是否都被覆盖）
    - 表达流畅度（语句是否流畅，过渡是否自然）
    - 语法和词汇（语法是否正确，词汇选择是否恰当）
    - 理解深度（对内容的理解是否深入，有无独到见解）

2. 以如下格式输出：
{
  "内容完整性": "对内容完整性的评价说明",
  "表达流畅度": "对表达流畅度的评价说明",
  "语法和词汇": "对语法和词汇的评价说明",
  "理解深度": "对理解深度的评价说明",
  "总体评价": "总体评价和简短建议"
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
- 每个维度的评价说明应简明扼要，不超过 50 字。
- 仅围绕用户提供的复述内容进行分析，不回答与表达能力分析无关的话题。`,
          },
          {
            role: 'user',
            content: '请根据以下复述点评：' + retell,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }

      const completion = await openai.chat.completions.create(params)
      let content = completion.choices[0]?.message?.content

      if (!content) {
        throw new Error('AI 响应为空')
      }

      const result = parseAIResponse(content)

      // 直接返回解析后的结果，保持与原接口完全兼容
      return result
    } catch (error) {
      console.error('点评复述失败：', error)
      // 直接抛出异常，保持与原接口行为一致
      throw error
    }
  },

  /**
   * 点评关键词故事
   * @param {string} story 故事内容
   * @param {Array} keywords 关键词数组
   * @returns {Object} 包含评价结果的响应对象
   */
  async evaluateKeywordStory(story, keywords) {
    if (!story) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '故事内容不能为空',
      }
    }

    if (!keywords || !Array.isArray(keywords)) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '关键词不能为空',
      }
    }

    try {
      const openai = new OpenAI(doubaoParams)

      const params = {
        model: 'doubao-seed-1-6-flash-250715',
        messages: [
          {
            role: 'system',
            content: `# 角色
      你是一位专业的创意写作评论家，擅长分析故事的创意、结构和表达，并能够提供有建设性的建议和创意示例。

      ## 技能
      ### 技能 1: 故事分析与创作
      1. 从以下几个维度分析用户创作的故事：
          - 关键词使用（是否自然、巧妙地融入了所有给定的关键词）
          - 故事创意（故事是否新颖、有趣，情节是否吸引人）
          - 表达流畅度（语句是否通顺，叙事节奏是否得当）
          - 语法和词汇（语法是否正确，词汇运用是否丰富、生动，并给出 3-5 个可以替换的更优词汇或语法结构建议）

      2. 创作两个额外的故事：
          - AI 示例故事：基于给定关键词创作一个简短的示例故事（150 字以内）
          - 故事改良版：基于用户的原始故事进行改良，保持原意但提升表达和创意（150 字以内）

      3. 以如下格式输出：
      {
        "关键词使用": "对关键词使用情况的评价说明",
        "故事创意": "对故事创意的评价说明",
        "表达流畅度": "对表达流畅度的评价说明",
        "语法和词汇": "对语法和词汇的评价说明，包含 3-5 个具体的词汇或语法改进建议",
        "AI 示例故事": "基于关键词创作的示例故事",
        "故事改良版": "对用户故事的改良版本",
        "总体评价": "对整个故事的总体评价和鼓励"
      }

      ## 限制：
      - 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
      - 每个评价维度的说明应简明扼要，不超过 60 字。
      - 示例故事和改良版故事各不超过 150 字。
      - 保持积极、鼓励的口吻。
      - 故事改良版应保留用户故事的核心内容和风格，但提升其表达和创意。`,
          },
          {
            role: 'user',
            content: `请根据以下关键词和故事进行点评：\n\n关键词：${keywords.join('，')}\n\n故事：${story}`,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }

      const completion = await openai.chat.completions.create(params)
      let content = completion.choices[0]?.message?.content

      if (!content) {
        throw new Error('AI 响应为空')
      }

      const result = parseAIResponse(content)

      // 直接返回解析后的结果，保持与原接口完全兼容
      return result
    } catch (error) {
      console.error('点评关键词故事失败：', error)
      // 直接抛出异常，保持与原接口行为一致
      throw error
    }
  },
}
